import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, Dimensions, Animated, Easing, ScrollView } from 'react-native';
import Swiper from 'react-native-swiper';
import { Button, Provider as <PERSON>Provider, IconButton } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

const slides = [
  {
    key: 'slide1',
    title: 'Track Every Walk',
    description: 'Log your pet walks, see routes, and keep memories.',
    image: require('../assets/logo-pawgo.png'),
  },
  {
    key: 'slide2',
    title: 'Discover Dog-Friendly Places',
    description: 'Find parks, cafes, and more for your furry friend.',
    image: require('../assets/logo-pawgo.png'),
  },
  {
    key: 'slide3',
    title: 'Earn Badges & Milestones',
    description: 'Celebrate your pet’s achievements and milestones.',
    image: require('../assets/logo-pawgo.png'),
  },
];

// Previous SplitText: word-by-word fade-in
function SplitText({ text }: { text: string }) {
  const words = text.split(' ');
  return (
    <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center' }}>
      {words.map((word, i) => {
        const fadeAnim = React.useRef(new Animated.Value(0)).current;
        React.useEffect(() => {
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 400,
            delay: i * 200,
            useNativeDriver: true,
            easing: Easing.out(Easing.ease),
          }).start();
        }, []);
        return (
          <Animated.Text key={i} style={[styles.splitWord, { opacity: fadeAnim }]}> {word} </Animated.Text>
        );
      })}
    </View>
  );
}

function BackArrow({ onPress }: { onPress: () => void }) {
  return (
    <IconButton
      icon="arrow-left"
      size={32}
      onPress={onPress}
      style={styles.backArrowBtn}
      iconColor="#B8860B"
      accessibilityLabel="Back"
    />
  );
}

function ForwardArrow({ onPress }: { onPress: () => void }) {
  return (
    <IconButton
      icon="arrow-right"
      size={32}
      onPress={onPress}
      style={styles.forwardArrowBtn}
      iconColor="#B8860B"
      accessibilityLabel="Next"
    />
  );
}

export default function OnboardingScreen() {
  const navigation = useNavigation();
  const [showFinal, setShowFinal] = useState(false);
  const [showGetStartedScreen, setShowGetStartedScreen] = useState(false);
  const [swiperIndex, setSwiperIndex] = useState(0);

  const completeOnboarding = (mode: 'login' | 'signup') => {
    (navigation as any).navigate('Auth', { mode });
  };

  if (showGetStartedScreen) {
    return (
      <PaperProvider>
        <ScrollView contentContainerStyle={styles.getStartedScreen} keyboardShouldPersistTaps="handled">
          <BackArrow onPress={() => setShowGetStartedScreen(false)} />
          <Image source={require('../assets/logo-pawgo.png')} style={styles.getStartedLogo} />
          <Button
            mode="contained"
            style={styles.getStartedScreenBtn}
            labelStyle={styles.getStartedScreenLabel}
            onPress={() => completeOnboarding('login')}
          >
            Login
          </Button>
          <Button
            mode="outlined"
            style={styles.getStartedScreenBtnOutlined}
            labelStyle={styles.getStartedScreenLabelOutlined}
            onPress={() => completeOnboarding('signup')}
          >
            Sign Up
          </Button>
        </ScrollView>
      </PaperProvider>
    );
  }

  return (
    <PaperProvider>
      <View style={{ flex: 1, backgroundColor: '#FFF8E1' }}>
        {!showFinal ? (
          <Swiper
            loop={false}
            showsPagination={true}
            activeDotColor="#B8860B"
            index={swiperIndex}
            onIndexChanged={i => {
              setSwiperIndex(i);
              if (i === slides.length) setShowFinal(true);
            }}
            dotStyle={{ marginBottom: 32 }}
            activeDotStyle={{ marginBottom: 32 }}
          >
            {slides.map((slide, idx) => (
              <View key={slide.key} style={styles.slide}>
                {/* Back arrow (not on first slide) */}
                {idx > 0 && (
                  <BackArrow onPress={() => setSwiperIndex(idx - 1)} />
                )}
                {/* Forward arrow (not on last slide) */}
                {idx < slides.length - 1 && (
                  <ForwardArrow onPress={() => setSwiperIndex(idx + 1)} />
                )}
                {/* Only show the logo image, remove any other arrow image */}
                <Image source={slide.image} style={styles.slideImage} />
                <SplitText text={slide.title} />
                <Text style={styles.slideDesc}>{slide.description}</Text>
                {idx === slides.length - 1 && (
                  <View style={styles.getStartedContainer}>
                    <Button
                      mode="contained"
                      style={styles.getStartedBtnSmall}
                      labelStyle={styles.getStartedLabel}
                      onPress={() => setShowGetStartedScreen(true)}
                    >
                      Get Started
                    </Button>
                  </View>
                )}
              </View>
            ))}
          </Swiper>
        ) : null}
      </View>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    position: 'relative',
  },
  slideImage: {
    width: width * 0.5,
    height: width * 0.5,
    resizeMode: 'contain',
    marginBottom: 32,
  },
  splitWord: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#B8860B',
    textAlign: 'center',
  },
  slideDesc: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
    marginTop: 12,
  },
  getStartedContainer: {
    position: 'absolute',
    bottom: 80,
    width: '100%',
    alignItems: 'center',
  },
  getStartedBtn: {
    backgroundColor: '#B8860B',
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    borderRadius: 16,
  },
  getStartedLabel: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  getStartedBtnSmall: {
    backgroundColor: '#B8860B',
    width: width * 0.5,
    height: 44,
    justifyContent: 'center',
    borderRadius: 16,
  },
  getStartedScreen: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    padding: 32,
  },
  getStartedLogo: {
    width: 200,
    height: 200,
    marginBottom: 40,
    resizeMode: 'contain',
  },
  getStartedScreenBtn: {
    backgroundColor: '#B8860B',
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    borderRadius: 16,
    marginBottom: 24,
  },
  getStartedScreenBtnOutlined: {
    borderColor: '#B8860B',
    borderWidth: 2,
    backgroundColor: '#FFF8E1',
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    borderRadius: 16,
    marginBottom: 24,
  },
  getStartedScreenLabel: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  getStartedScreenLabelOutlined: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#B8860B',
  },
  backArrowBtn: {
    position: 'absolute',
    top: 64,
    left: 16,
    zIndex: 10,
    minWidth: 48,
    minHeight: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  backArrowLabel: {
    fontSize: 28,
    color: '#B8860B',
  },
  forwardArrowBtn: {
    position: 'absolute',
    top: 64,
    right: 16,
    zIndex: 10,
    minWidth: 48,
    minHeight: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  forwardArrowLabel: {
    fontSize: 28,
    color: '#B8860B',
  },
}); 