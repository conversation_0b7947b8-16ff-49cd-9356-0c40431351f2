import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, Dimensions, Animated, Easing, ScrollView } from 'react-native';
import Swiper from 'react-native-swiper';
import { Button, Provider as <PERSON>Provider, IconButton } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

const slides = [
  {
    key: 'slide1',
    title: 'Track Every Walk',
    description: 'Log your pet walks, see routes, and keep memories.',
    image: require('../assets/logo-pawgo.png'),
  },
  {
    key: 'slide2',
    title: 'Discover Dog-Friendly Places',
    description: 'Find parks, cafes, and more for your furry friend.',
    image: require('../assets/logo-pawgo.png'),
  },
  {
    key: 'slide3',
    title: 'Earn Badges & Milestones',
    description: 'Celebrate your pet’s achievements and milestones.',
    image: require('../assets/logo-pawgo.png'),
  },
];

// Previous SplitText: word-by-word fade-in
function SplitText({ text }: { text: string }) {
  const words = text.split(' ');
  return (
    <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'center' }}>
      {words.map((word, i) => {
        const fadeAnim = React.useRef(new Animated.Value(0)).current;
        React.useEffect(() => {
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 400,
            delay: i * 200,
            useNativeDriver: true,
            easing: Easing.out(Easing.ease),
          }).start();
        }, []);
        return (
          <Animated.Text key={i} style={[styles.splitWord, { opacity: fadeAnim }]}> {word} </Animated.Text>
        );
      })}
    </View>
  );
}

// Removed arrow components to fix logo display issue

export default function OnboardingScreen() {
  const navigation = useNavigation();
  const [showFinal, setShowFinal] = useState(false);
  const [showGetStartedScreen, setShowGetStartedScreen] = useState(false);
  const [swiperIndex, setSwiperIndex] = useState(0);

  const completeOnboarding = (mode: 'login' | 'signup') => {
    (navigation as any).navigate('Auth', { mode });
  };

  if (showGetStartedScreen) {
    return (
      <PaperProvider>
        <ScrollView contentContainerStyle={styles.getStartedScreen} keyboardShouldPersistTaps="handled">
          <Image
            source={require('../assets/logo-pawgo.png')}
            style={styles.getStartedLogo}
            onError={(error) => console.log('Get started logo load error:', error)}
            onLoad={() => console.log('Get started logo loaded successfully')}
          />
          <Button
            mode="contained"
            style={styles.getStartedScreenBtn}
            labelStyle={styles.getStartedScreenLabel}
            onPress={() => completeOnboarding('login')}
          >
            Login
          </Button>
          <Button
            mode="outlined"
            style={styles.getStartedScreenBtnOutlined}
            labelStyle={styles.getStartedScreenLabelOutlined}
            onPress={() => completeOnboarding('signup')}
          >
            Sign Up
          </Button>
        </ScrollView>
      </PaperProvider>
    );
  }

  return (
    <PaperProvider>
      <View style={{ flex: 1, backgroundColor: '#FFF8E1' }}>
        {!showFinal ? (
          <Swiper
            loop={false}
            showsPagination={true}
            activeDotColor="#B8860B"
            index={swiperIndex}
            onIndexChanged={i => {
              setSwiperIndex(i);
              if (i === slides.length) setShowFinal(true);
            }}
            dotStyle={{ marginBottom: 32 }}
            activeDotStyle={{ marginBottom: 32 }}
          >
            {slides.map((slide, idx) => (
              <View key={slide.key} style={styles.slide}>
                {/* Only show the logo image, arrows removed */}
                <Image
                  source={slide.image}
                  style={styles.slideImage}
                  onError={(error) => console.log('Slide image load error:', error)}
                  onLoad={() => console.log('Slide image loaded successfully')}
                />
                <SplitText text={slide.title} />
                <Text style={styles.slideDesc}>{slide.description}</Text>
                {idx === slides.length - 1 && (
                  <View style={styles.getStartedContainer}>
                    <Button
                      mode="contained"
                      style={styles.getStartedBtnSmall}
                      labelStyle={styles.getStartedLabel}
                      onPress={() => setShowGetStartedScreen(true)}
                    >
                      Get Started
                    </Button>
                  </View>
                )}
              </View>
            ))}
          </Swiper>
        ) : null}
      </View>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
    position: 'relative',
  },
  slideImage: {
    width: width * 0.6,
    height: width * 0.6,
    resizeMode: 'contain',
    marginBottom: 32,
    backgroundColor: 'transparent',
  },
  splitWord: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#B8860B',
    textAlign: 'center',
  },
  slideDesc: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
    marginTop: 12,
  },
  getStartedContainer: {
    position: 'absolute',
    bottom: 80,
    width: '100%',
    alignItems: 'center',
  },
  getStartedBtn: {
    backgroundColor: '#B8860B',
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    borderRadius: 16,
  },
  getStartedLabel: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  getStartedBtnSmall: {
    backgroundColor: '#B8860B',
    width: width * 0.5,
    height: 44,
    justifyContent: 'center',
    borderRadius: 16,
  },
  getStartedScreen: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    padding: 32,
  },
  getStartedLogo: {
    width: 250,
    height: 250,
    marginBottom: 40,
    resizeMode: 'contain',
    backgroundColor: 'transparent',
  },
  getStartedScreenBtn: {
    backgroundColor: '#B8860B',
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    borderRadius: 16,
    marginBottom: 24,
  },
  getStartedScreenBtnOutlined: {
    borderColor: '#B8860B',
    borderWidth: 2,
    backgroundColor: '#FFF8E1',
    width: width * 0.8,
    height: 60,
    justifyContent: 'center',
    borderRadius: 16,
    marginBottom: 24,
  },
  getStartedScreenLabel: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#fff',
  },
  getStartedScreenLabelOutlined: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#B8860B',
  },
  // Arrow styles removed to fix logo display
}); 