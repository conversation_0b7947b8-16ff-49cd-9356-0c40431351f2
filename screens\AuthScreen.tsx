import React, { useState } from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { TextInput, Button, Text, useTheme, Provider as PaperProvider, IconButton } from 'react-native-paper';
import { supabase } from '../lib/supabase';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';

WebBrowser.maybeCompleteAuthSession();

export default function AuthScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const theme = useTheme();

  // Google Auth
  const [request, response, promptAsync] = Google.useAuthRequest({
    clientId: process.env.EXPO_PUBLIC_GOOGLE_EXPO_CLIENT_ID,
    iosClientId: process.env.EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID,
    androidClientId: process.env.EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID,
    webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
  });

  React.useEffect(() => {
    if (response?.type === 'success') {
      const { authentication } = response;
      if (authentication?.accessToken) {
        // Sign in with Supabase using Google OAuth token
        supabase.auth.signInWithIdToken({
          provider: 'google',
          token: authentication.accessToken,
        });
      }
    }
  }, [response]);

  const handleAuth = async () => {
    setLoading(true);
    setError('');
    try {
      if (isSignUp) {
        const { error } = await supabase.auth.signUp({ email, password });
        if (error) throw error;
      } else {
        const { error } = await supabase.auth.signInWithPassword({ email, password });
        if (error) throw error;
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PaperProvider>
      <View style={styles.container}>
        <Image source={require('../assets/logo-pawgo.png')} style={styles.logo} />
        <Text style={styles.title}>Welcome to PawGo!</Text>
        <TextInput
          label="Email"
          value={email}
          onChangeText={setEmail}
          style={styles.input}
          autoCapitalize="none"
          keyboardType="email-address"
        />
        <TextInput
          label="Password"
          value={password}
          onChangeText={setPassword}
          style={styles.input}
          secureTextEntry={!showPassword}
          right={<TextInput.Icon icon={showPassword ? 'eye-off' : 'eye'} onPress={() => setShowPassword(v => !v)} />}
        />
        {error ? <Text style={styles.error}>{error}</Text> : null}
        <Button
          mode="contained"
          onPress={handleAuth}
          loading={loading}
          style={styles.button}
          contentStyle={{ paddingVertical: 8 }}
        >
          {isSignUp ? 'Sign Up' : 'Login'}
        </Button>
        <Button
          mode="outlined"
          onPress={() => setIsSignUp(!isSignUp)}
          style={styles.switchButton}
        >
          {isSignUp ? 'Already have an account? Login' : "Don't have an account? Sign Up"}
        </Button>
        <Button
          icon="google"
          mode="contained"
          onPress={() => promptAsync()}
          disabled={!request}
          style={styles.googleButton}
          contentStyle={{ paddingVertical: 8 }}
        >
          Sign in with Google
        </Button>
      </View>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF8E1', // warm cream
    padding: 20,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#B8860B', // golden
    marginBottom: 24,
  },
  input: {
    width: '100%',
    marginBottom: 12,
    backgroundColor: '#FFF',
  },
  button: {
    width: '100%',
    backgroundColor: '#B8860B', // golden
    marginBottom: 8,
  },
  switchButton: {
    width: '100%',
    borderColor: '#B8860B',
    marginBottom: 16,
  },
  googleButton: {
    width: '100%',
    backgroundColor: '#00BFAE', // teal highlight
  },
  error: {
    color: '#D32F2F',
    marginBottom: 8,
  },
}); 