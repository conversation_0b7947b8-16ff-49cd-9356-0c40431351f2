import React, { useState } from 'react';
import { View, Image, StyleSheet, Alert } from 'react-native';
import { TextInput, Button, Text, useTheme, Provider as PaperProvider, IconButton } from 'react-native-paper';
import { supabase } from '../lib/supabase';
import * as WebBrowser from 'expo-web-browser';
import * as Google from 'expo-auth-session/providers/google';
import * as Linking from 'expo-linking';

WebBrowser.maybeCompleteAuthSession();

export default function AuthScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [isSignUp, setIsSignUp] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const theme = useTheme();

  // Google Auth - Using proper Expo redirect URI
  const [request, response, promptAsync] = Google.useAuthRequest({
    webClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
    // For development, we'll use the web client ID for Android too
    androidClientId: process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID,
    redirectUri: 'https://auth.expo.io/@mrkingpt/PawGo',
  });

  React.useEffect(() => {
    if (response?.type === 'success') {
      const { authentication } = response;
      if (authentication?.accessToken) {
        // Sign in with Supabase using Google OAuth token
        handleGoogleSignIn(authentication.accessToken);
      }
    } else if (response?.type === 'error') {
      console.error('Google OAuth error:', response.error);
      setError('Google sign-in failed. Please try again.');
    }
  }, [response]);

  const handleGoogleSignIn = async (accessToken: string) => {
    try {
      setLoading(true);
      console.log('Signing in with Google token...');

      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: accessToken,
      });

      if (error) throw error;

      console.log('Google sign-in successful:', data);
      // Navigation will be handled by the auth state listener in App.tsx

    } catch (err: any) {
      console.error('Supabase Google sign-in error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleOAuth = async () => {
    try {
      console.log('Starting Google OAuth...');
      // Use the expo-auth-session approach
      promptAsync();
    } catch (err: any) {
      console.error('Google OAuth error:', err);
      setError(err.message);
      Alert.alert('Authentication Error', err.message);
    }
  };

  const handleAuth = async () => {
    setLoading(true);
    setError('');
    try {
      if (isSignUp) {
        const { error } = await supabase.auth.signUp({ email, password });
        if (error) throw error;
      } else {
        const { error } = await supabase.auth.signInWithPassword({ email, password });
        if (error) throw error;
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PaperProvider>
      <View style={styles.container}>
        <View style={styles.logoContainer}>
          <Text style={styles.logoEmoji}>🐕</Text>
          <Text style={styles.logoTitle}>PawGo</Text>
        </View>
        <Text style={styles.title}>Welcome to PawGo!</Text>
        <TextInput
          label="Email"
          value={email}
          onChangeText={setEmail}
          style={styles.input}
          autoCapitalize="none"
          keyboardType="email-address"
        />
        <TextInput
          label="Password"
          value={password}
          onChangeText={setPassword}
          style={styles.input}
          secureTextEntry={!showPassword}
          right={<TextInput.Icon icon={showPassword ? 'eye-off' : 'eye'} onPress={() => setShowPassword(v => !v)} />}
        />
        {error ? <Text style={styles.error}>{error}</Text> : null}
        <Button
          mode="contained"
          onPress={handleAuth}
          loading={loading}
          style={styles.button}
          contentStyle={{ paddingVertical: 8 }}
        >
          {isSignUp ? 'Sign Up' : 'Login'}
        </Button>
        <Button
          mode="outlined"
          onPress={() => setIsSignUp(!isSignUp)}
          style={styles.switchButton}
        >
          {isSignUp ? 'Already have an account? Login' : "Don't have an account? Sign Up"}
        </Button>
        <Button
          icon="google"
          mode="contained"
          onPress={handleGoogleOAuth}
          style={styles.googleButton}
          contentStyle={{ paddingVertical: 8 }}
        >
          Sign in with Google
        </Button>
      </View>
    </PaperProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF8E1', // warm cream
    padding: 20,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  logoEmoji: {
    fontSize: 60,
    marginBottom: 8,
  },
  logoTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#B8860B',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#B8860B', // golden
    marginBottom: 24,
  },
  input: {
    width: '100%',
    marginBottom: 12,
    backgroundColor: '#FFF',
  },
  button: {
    width: '100%',
    backgroundColor: '#B8860B', // golden
    marginBottom: 8,
  },
  switchButton: {
    width: '100%',
    borderColor: '#B8860B',
    marginBottom: 16,
  },
  googleButton: {
    width: '100%',
    backgroundColor: '#00BFAE', // teal highlight
  },
  error: {
    color: '#D32F2F',
    marginBottom: 8,
  },
}); 