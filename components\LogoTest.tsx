import React from 'react';
import { View, Image, Text, StyleSheet } from 'react-native';

export default function LogoTest() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Logo Test</Text>
      <Image 
        source={require('../assets/logo-pawgo.png')} 
        style={styles.logo}
        onError={(error) => console.log('Logo load error:', error)}
        onLoad={() => console.log('Logo loaded successfully')}
      />
      <Text style={styles.description}>
        If you see the PawGo logo above, the image is working correctly.
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFF8E1',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#B8860B',
    marginBottom: 20,
  },
  logo: {
    width: 150,
    height: 150,
    marginBottom: 20,
    resizeMode: 'contain',
  },
  description: {
    fontSize: 16,
    color: '#8B4513',
    textAlign: 'center',
    marginTop: 20,
  },
});
