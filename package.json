{"name": "pawgo", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@react-spring/web": "^10.0.1", "@supabase/supabase-js": "^2.50.5", "expo": "~53.0.17", "expo-auth-session": "^6.2.1", "expo-status-bar": "~2.2.3", "framer-motion": "^12.23.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.11.1", "react-native-swiper": "^1.6.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}