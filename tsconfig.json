{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["esnext"], "jsx": "react-native", "moduleResolution": "node", "allowJs": true, "noEmit": true, "isolatedModules": true, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "types": ["react", "react-native", "expo", "expo-auth-session"], "baseUrl": ".", "paths": {"*": ["node_modules/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base"}